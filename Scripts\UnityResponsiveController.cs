using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

/// <summary>
/// Unity-specific responsive controller that properly handles UI Toolkit responsive design
/// Works with Unity's screen size detection and applies styles programmatically
/// </summary>
public class UnityResponsiveController : MonoBehaviour
{
    [Header("📱 Unity Responsive Controller")]
    [TextArea(3, 5)]
    public string info = @"
This script handles responsive design specifically for Unity UI Toolkit:
- Detects screen size changes in real-time
- Applies responsive styles programmatically
- Works properly with Unity simulators and devices
- Handles iPhone, iPad, and Android screen sizes";

    [Header("Settings")]
    public bool enableResponsiveMode = true;
    public bool logScreenChanges = true;
    public bool showDebugInfo = false;
    
    [Header("Breakpoints (Unity pixels)")]
    public int phoneMaxWidth = 480;
    public int largePhoneMaxWidth = 600;
    public int tabletMaxWidth = 800;
    public int largeTabletMaxWidth = 1024;
    
    // Private variables
    private UIDocument uiDocument;
    private VisualElement root;
    private Vector2 lastScreenSize;
    private string currentDeviceType = "";
    
    // UI Elements
    private VisualElement characterImage;
    private VisualElement dialogueBox;
    private VisualElement nameBox;
    private Label characterName;
    private Label dialogueText;
    private VisualElement topBar;
    private VisualElement choicesContainer;
    private Button nextButton;
    
    private void Start()
    {
        InitializeController();
        if (enableResponsiveMode)
        {
            StartCoroutine(MonitorScreenSize());
        }
    }
    
    private void InitializeController()
    {
        Debug.Log("📱 Initializing Unity Responsive Controller...");
        
        uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("❌ UIDocument not found! Add UIDocument component first.");
            enabled = false;
            return;
        }
        
        root = uiDocument.rootVisualElement;
        if (root == null)
        {
            Debug.LogError("❌ Root visual element not found!");
            enabled = false;
            return;
        }
        
        // Cache UI elements
        CacheUIElements();
        
        // Set initial screen size
        lastScreenSize = new Vector2(Screen.width, Screen.height);
        
        // Apply initial responsive styles
        ApplyResponsiveStyles();
        
        Debug.Log("✅ Unity Responsive Controller initialized!");
    }
    
    private void CacheUIElements()
    {
        characterImage = root.Q("character-image");
        dialogueBox = root.Q("dialogue-box");
        nameBox = root.Q("name-box");
        characterName = root.Q<Label>("character-name");
        dialogueText = root.Q<Label>("dialogue-text");
        topBar = root.Q("top-bar");
        choicesContainer = root.Q("choices-container");
        nextButton = root.Q<Button>("next-button");
        
        if (logScreenChanges)
        {
            Debug.Log($"UI Elements cached: Character={characterImage != null}, Dialogue={dialogueBox != null}");
        }
    }
    
    private IEnumerator MonitorScreenSize()
    {
        while (enabled && enableResponsiveMode)
        {
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            
            if (currentScreenSize != lastScreenSize)
            {
                if (logScreenChanges)
                {
                    Debug.Log($"📱 Screen size changed: {lastScreenSize} → {currentScreenSize}");
                }
                
                lastScreenSize = currentScreenSize;
                ApplyResponsiveStyles();
            }
            
            yield return new WaitForSeconds(0.2f); // Check 5 times per second
        }
    }
    
    private void ApplyResponsiveStyles()
    {
        string deviceType = GetDeviceType();
        
        if (deviceType != currentDeviceType)
        {
            currentDeviceType = deviceType;
            
            if (logScreenChanges)
            {
                Debug.Log($"📱 Device type: {deviceType} ({Screen.width}x{Screen.height})");
            }
            
            // Apply device-specific styles
            ApplyCharacterImageStyles(deviceType);
            ApplyDialogueBoxStyles(deviceType);
            ApplyTopBarStyles(deviceType);
            ApplyTextStyles(deviceType);
            ApplyButtonStyles(deviceType);
        }
    }
    
    private string GetDeviceType()
    {
        int width = Screen.width;
        
        if (width <= phoneMaxWidth)
            return "phone";
        else if (width <= largePhoneMaxWidth)
            return "large-phone";
        else if (width <= tabletMaxWidth)
            return "tablet";
        else if (width <= largeTabletMaxWidth)
            return "large-tablet";
        else
            return "desktop";
    }
    
    private void ApplyCharacterImageStyles(string deviceType)
    {
        if (characterImage == null) return;
        
        // Character image is always at bottom: 0
        characterImage.style.position = Position.Absolute;
        characterImage.style.bottom = 0;
        characterImage.style.left = new StyleLength(new Length(50, LengthUnit.Percent));
        characterImage.style.translate = new StyleTranslate(new Translate(new Length(-50, LengthUnit.Percent), 0));
        
        // Size based on device type
        switch (deviceType)
        {
            case "phone":
                characterImage.style.width = 200;
                characterImage.style.height = 300;
                break;
            case "large-phone":
                characterImage.style.width = 250;
                characterImage.style.height = 375;
                break;
            case "tablet":
                characterImage.style.width = 300;
                characterImage.style.height = 450;
                break;
            case "large-tablet":
                characterImage.style.width = 350;
                characterImage.style.height = 525;
                break;
            default: // desktop
                characterImage.style.width = 400;
                characterImage.style.height = 600;
                break;
        }
    }
    
    private void ApplyDialogueBoxStyles(string deviceType)
    {
        if (dialogueBox == null) return;
        
        switch (deviceType)
        {
            case "phone":
                dialogueBox.style.width = new StyleLength(new Length(98, LengthUnit.Percent));
                dialogueBox.style.paddingLeft = 8;
                dialogueBox.style.paddingRight = 8;
                dialogueBox.style.paddingTop = 8;
                dialogueBox.style.paddingBottom = 8;
                dialogueBox.style.minHeight = 90;
                break;
            case "large-phone":
                dialogueBox.style.width = new StyleLength(new Length(96, LengthUnit.Percent));
                dialogueBox.style.paddingLeft = 10;
                dialogueBox.style.paddingRight = 10;
                dialogueBox.style.paddingTop = 10;
                dialogueBox.style.paddingBottom = 10;
                dialogueBox.style.minHeight = 100;
                break;
            case "tablet":
                dialogueBox.style.width = new StyleLength(new Length(95, LengthUnit.Percent));
                dialogueBox.style.paddingLeft = 12;
                dialogueBox.style.paddingRight = 12;
                dialogueBox.style.paddingTop = 12;
                dialogueBox.style.paddingBottom = 12;
                dialogueBox.style.minHeight = 120;
                break;
            default:
                dialogueBox.style.width = new StyleLength(new Length(90, LengthUnit.Percent));
                dialogueBox.style.paddingLeft = 15;
                dialogueBox.style.paddingRight = 15;
                dialogueBox.style.paddingTop = 15;
                dialogueBox.style.paddingBottom = 15;
                dialogueBox.style.minHeight = 150;
                break;
        }
    }
    
    private void ApplyTopBarStyles(string deviceType)
    {
        if (topBar == null) return;
        
        var buttons = topBar.Query<Button>().ToList();
        
        foreach (var button in buttons)
        {
            switch (deviceType)
            {
                case "phone":
                    button.style.width = 40;
                    button.style.height = 22;
                    button.style.fontSize = 9;
                    break;
                case "large-phone":
                    button.style.width = 50;
                    button.style.height = 28;
                    button.style.fontSize = 11;
                    break;
                case "tablet":
                    button.style.width = 60;
                    button.style.height = 32;
                    button.style.fontSize = 12;
                    break;
                default:
                    button.style.width = 80;
                    button.style.height = 40;
                    button.style.fontSize = 14;
                    break;
            }
        }
    }
    
    private void ApplyTextStyles(string deviceType)
    {
        // Character name
        if (characterName != null)
        {
            switch (deviceType)
            {
                case "phone":
                    characterName.style.fontSize = 12;
                    break;
                case "large-phone":
                    characterName.style.fontSize = 14;
                    break;
                case "tablet":
                    characterName.style.fontSize = 15;
                    break;
                default:
                    characterName.style.fontSize = 18;
                    break;
            }
        }
        
        // Dialogue text
        if (dialogueText != null)
        {
            switch (deviceType)
            {
                case "phone":
                    dialogueText.style.fontSize = 11;
                    break;
                case "large-phone":
                    dialogueText.style.fontSize = 13;
                    break;
                case "tablet":
                    dialogueText.style.fontSize = 14;
                    break;
                default:
                    dialogueText.style.fontSize = 16;
                    break;
            }
        }
        
        // Name box
        if (nameBox != null)
        {
            switch (deviceType)
            {
                case "phone":
                    nameBox.style.width = 150;
                    nameBox.style.height = 32;
                    break;
                case "large-phone":
                    nameBox.style.width = 180;
                    nameBox.style.height = 35;
                    break;
                case "tablet":
                    nameBox.style.width = 220;
                    nameBox.style.height = 38;
                    break;
                default:
                    nameBox.style.width = 300;
                    nameBox.style.height = 50;
                    break;
            }
        }
    }
    
    private void ApplyButtonStyles(string deviceType)
    {
        if (nextButton == null) return;
        
        switch (deviceType)
        {
            case "phone":
                nextButton.style.width = 20;
                nextButton.style.height = 20;
                nextButton.style.fontSize = 12;
                break;
            case "large-phone":
                nextButton.style.width = 25;
                nextButton.style.height = 25;
                nextButton.style.fontSize = 14;
                break;
            case "tablet":
                nextButton.style.width = 28;
                nextButton.style.height = 28;
                nextButton.style.fontSize = 16;
                break;
            default:
                nextButton.style.width = 30;
                nextButton.style.height = 30;
                nextButton.style.fontSize = 20;
                break;
        }
    }
    
    // Public methods
    public void ForceUpdate()
    {
        ApplyResponsiveStyles();
    }
    
    public string GetCurrentDeviceType()
    {
        return currentDeviceType;
    }
    
    private void OnGUI()
    {
        if (showDebugInfo)
        {
            GUILayout.BeginArea(new Rect(10, Screen.height - 120, 250, 110));
            GUILayout.Box("Unity Responsive Debug", GUI.skin.box);
            GUILayout.Label($"Screen: {Screen.width}x{Screen.height}");
            GUILayout.Label($"Device: {currentDeviceType}");
            GUILayout.Label($"DPI: {Screen.dpi}");
            GUILayout.Label($"Responsive: {enableResponsiveMode}");
            GUILayout.EndArea();
        }
    }
}
