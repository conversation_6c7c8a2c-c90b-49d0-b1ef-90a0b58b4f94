/* Unity USS Styles for Story Page - Visual Novel Layout */

/* Root Story Container */
.story-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    background-color: rgb(0, 0, 0);
    background-image: url('project://database/Assets/Resources/Background/taman paradise (1).png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Background Image */
.background-image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-image: url('project://database/Assets/Resources/Background/taman paradise (1).png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: rgb(20, 20, 40);
}

/* Character Container */
.character-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: 180px; /* Space for dialogue box */
}

/* Character Image */
.character-image {
    width: 400px;
    height: 600px;
    background-size: contain;
    background-position: center bottom;
    background-repeat: no-repeat;
    transition-duration: 0.5s;
    transition-property: opacity, translate;
    align-self: center; /* Center horizontally but stay at bottom */
}

/* UI Overlay */
.ui-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    justify-content: flex-end;
    flex-direction: column;
}

/* Top UI Bar */
.top-bar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
    padding-top: 10px;
    background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 0;
    left: 0;
}

.top-button {
    width: 80px;
    height: 40px;
    margin-left: 10px;
    background-color: rgba(50, 50, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 1px;
    border-radius: 5px;
    color: rgb(255, 255, 255);
    font-size: 14px;
    transition-duration: 0.2s;
}

.top-button:hover {
    background-color: rgba(80, 80, 80, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    scale: 1.05;
}

.top-button:active {
    background-color: rgba(100, 100, 100, 0.9);
    scale: 0.95;
}

.top-button.active {
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);
    color: rgb(255, 255, 255);
}

/* Dialogue Area */
.dialogue-area {
    width: 100%;
    flex-direction: column;
    align-items: center;
    padding-bottom: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
}

/* Character Name Box */
.name-box {
    width: 300px;
    height: 50px;
    background-color: rgba(30, 30, 60, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 10px 10px 0 0;
    justify-content: center;
    align-items: center;
    margin-bottom: -2px;
}

.character-name {
    color: rgb(255, 255, 255);
    font-size: 18px;
    font-style: bold;
    text-align: middle-center;
    -unity-text-align: middle-center;
}

/* Dialogue Box */
.dialogue-box {
    width: 90%;
    min-height: 150px;
    max-width: 1000px;
    background-color: rgba(20, 20, 40, 0.95);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 15px;
    padding: 10px;
    position: relative;
    flex-direction: row;
    align-items: flex-end;
}

.dialogue-text {
    color: rgb(255, 255, 255);
    font-size: 16px;
    line-height: 24px;
    white-space: normal;
    flex-grow: 1;
    margin-right: 60px;
    -unity-text-align: upper-left;
}

/* Next Button */
.next-button {
    width: 30px;
    height: 30px;
    position: absolute;
    bottom: 15px;
    right: 15px;
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.5);
    border-width: 2px;
    border-radius: 25px;
    color: rgb(255, 255, 255);
    font-size: 20px;
    font-style: bold;
    transition-duration: 0.2s;
}

.next-button:hover {
    background-color: rgba(120, 170, 255, 0.9);
    scale: 1.1;
}

.next-button:active {
    background-color: rgba(80, 130, 255, 1);
    scale: 0.9;
}

/* Choices Container */
.choices-container {
    width: 90%;
    max-width: 800px;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    position: absolute;
    bottom: 180px;
    left: 50%;
    translate: -50% 0;
}

.choice-button {
    width: 100%;
    min-height: 50px;
    margin-bottom: 10px;
    background-color: rgba(60, 60, 100, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 10px;
    color: rgb(255, 255, 255);
    font-size: 16px;
    padding: 10px 20px;
    transition-duration: 0.2s;
    white-space: normal;
}

.choice-button:hover {
    background-color: rgba(80, 80, 120, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    scale: 1.02;
}

.choice-button:active {
    background-color: rgba(100, 100, 140, 0.9);
    scale: 0.98;
}

/* Fade Overlay for Transitions */
.fade-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgb(0, 0, 0);
    opacity: 0;
    transition-duration: 0.5s;
    transition-property: opacity;
}

/* Animation Classes */
.fade-in {
    opacity: 1;
}

.fade-out {
    opacity: 0;
}

.slide-in-left {
    translate: -100px 0;
    opacity: 0;
}

.slide-in-right {
    translate: 100px 0;
    opacity: 0;
}

.slide-in-center {
    translate: 0 0;
    opacity: 1;
}

/* Responsive Transition Classes */
.responsive-transition {
    transition-duration: 0.3s;
    transition-property: width, height, font-size, padding, margin;
    transition-timing-function: ease-in-out;
}

.responsive-transition .dialogue-box {
    transition-duration: 0.3s;
    transition-property: width, height, padding, min-height;
}

.responsive-transition .character-image {
    transition-duration: 0.3s;
    transition-property: width, height;
}

.responsive-transition .top-button {
    transition-duration: 0.3s;
    transition-property: width, height, font-size;
}

.responsive-transition .choice-button {
    transition-duration: 0.3s;
    transition-property: font-size, padding, min-height;
}

/* Character Position Classes - All characters centered for Maimy story */
.character-left {
    align-self: center;
}

.character-center {
    align-self: center;
}

.character-right {
    align-self: center;
}

/* Responsive Design - Enhanced for Multiple Screen Sizes */

/* Large Tablets and Small Desktops (1024px and below) */
@media (max-width: 1024px) {
    .character-image {
        width: 350px;
        height: 525px;
    }

    .dialogue-box {
        width: 92%;
        padding: 12px;
    }

    .name-box {
        width: 280px;
        height: 45px;
    }

    .character-name {
        font-size: 16px;
    }

    .dialogue-text {
        font-size: 15px;
        line-height: 22px;
    }

    .top-button {
        width: 70px;
        height: 35px;
        font-size: 13px;
    }

    .choice-button {
        font-size: 15px;
        padding: 8px 15px;
    }
}

/* Standard Tablets (800px and below) */
@media (max-width: 800px) {
    .character-image {
        width: 300px;
        height: 450px;
    }

    .dialogue-box {
        width: 95%;
        padding: 15px;
        min-height: 140px;
    }

    .name-box {
        width: 260px;
        height: 42px;
    }

    .character-name {
        font-size: 15px;
    }

    .dialogue-text {
        font-size: 14px;
        line-height: 20px;
        margin-right: 50px;
    }

    .top-button {
        width: 60px;
        height: 32px;
        font-size: 12px;
        margin-left: 8px;
    }

    .top-bar {
        height: 55px;
        padding-right: 15px;
    }

    .next-button {
        width: 28px;
        height: 28px;
        font-size: 18px;
        bottom: 12px;
        right: 12px;
    }

    .choice-button {
        font-size: 14px;
        padding: 8px 15px;
        min-height: 45px;
    }

    .choices-container {
        bottom: 160px;
        width: 92%;
    }
}

/* Large Phones (600px and below) */
@media (max-width: 600px) {
    .character-image {
        width: 250px;
        height: 375px;
    }

    .dialogue-box {
        width: 96%;
        padding: 12px;
        min-height: 120px;
    }

    .name-box {
        width: 220px;
        height: 38px;
    }

    .character-name {
        font-size: 14px;
    }

    .dialogue-text {
        font-size: 13px;
        line-height: 18px;
        margin-right: 45px;
    }

    .top-button {
        width: 50px;
        height: 28px;
        font-size: 11px;
        margin-left: 6px;
    }

    .top-bar {
        height: 50px;
        padding-right: 12px;
        padding-top: 8px;
    }

    .next-button {
        width: 25px;
        height: 25px;
        font-size: 16px;
        bottom: 10px;
        right: 10px;
    }

    .choice-button {
        font-size: 13px;
        padding: 6px 12px;
        min-height: 40px;
        margin-bottom: 8px;
    }

    .choices-container {
        bottom: 140px;
        width: 94%;
    }

    .character-container {
        padding-bottom: 160px; /* Space for dialogue box on large phones */
    }
}

/* Standard Phones (480px and below) */
@media (max-width: 480px) {
    .character-image {
        width: 200px;
        height: 300px;
    }

    .dialogue-box {
        width: 98%;
        padding: 10px;
        min-height: 100px;
        border-radius: 12px;
    }

    .name-box {
        width: 180px;
        height: 35px;
        border-radius: 8px 8px 0 0;
    }

    .character-name {
        font-size: 13px;
    }

    .dialogue-text {
        font-size: 12px;
        line-height: 16px;
        margin-right: 40px;
    }

    .top-button {
        width: 45px;
        height: 25px;
        font-size: 10px;
        margin-left: 5px;
        border-radius: 4px;
    }

    .top-bar {
        height: 45px;
        padding-right: 10px;
        padding-top: 6px;
    }

    .next-button {
        width: 22px;
        height: 22px;
        font-size: 14px;
        bottom: 8px;
        right: 8px;
        border-radius: 20px;
    }

    .choice-button {
        font-size: 12px;
        padding: 5px 10px;
        min-height: 35px;
        margin-bottom: 6px;
        border-radius: 8px;
    }

    .choices-container {
        bottom: 120px;
        width: 96%;
    }

    .character-container {
        padding-bottom: 140px; /* Space for dialogue box on standard phones */
    }

    .dialogue-area {
        padding-bottom: 15px;
    }
}

/* Small Phones (360px and below) */
@media (max-width: 360px) {
    .character-image {
        width: 160px;
        height: 240px;
    }

    .dialogue-box {
        width: 99%;
        padding: 8px;
        min-height: 90px;
        border-radius: 10px;
    }

    .name-box {
        width: 150px;
        height: 32px;
        border-radius: 6px 6px 0 0;
    }

    .character-name {
        font-size: 12px;
    }

    .dialogue-text {
        font-size: 11px;
        line-height: 15px;
        margin-right: 35px;
    }

    .top-button {
        width: 40px;
        height: 22px;
        font-size: 9px;
        margin-left: 4px;
        border-radius: 3px;
    }

    .top-bar {
        height: 40px;
        padding-right: 8px;
        padding-top: 5px;
    }

    .next-button {
        width: 20px;
        height: 20px;
        font-size: 12px;
        bottom: 6px;
        right: 6px;
        border-radius: 18px;
    }

    .choice-button {
        font-size: 11px;
        padding: 4px 8px;
        min-height: 32px;
        margin-bottom: 5px;
        border-radius: 6px;
    }

    .choices-container {
        bottom: 100px;
        width: 98%;
    }

    .character-container {
        padding-bottom: 120px; /* Space for dialogue box on small phones */
    }

    .dialogue-area {
        padding-bottom: 12px;
    }
}

/* Portrait Orientation Adjustments */
@media (orientation: portrait) and (max-width: 600px) {
    .character-image {
        width: 220px;
        height: 330px;
    }

    .character-container {
        padding-bottom: 150px; /* Space for dialogue box in portrait mode */
    }
}

/* Landscape Orientation Adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .character-image {
        width: 180px;
        height: 270px;
    }

    .dialogue-box {
        min-height: 80px;
    }

    .character-container {
        padding-bottom: 110px; /* Space for dialogue box in landscape mode */
    }

    .choices-container {
        bottom: 90px;
    }

    .top-bar {
        height: 35px;
    }

    .top-button {
        height: 20px;
        font-size: 9px;
    }
}
