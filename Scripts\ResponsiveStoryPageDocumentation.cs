using UnityEngine;

/// <summary>
/// Documentation and usage guide for the Responsive Story Page system
/// Contains detailed instructions and troubleshooting information
/// </summary>
public class ResponsiveStoryPageDocumentation : MonoBehaviour
{
    [Header("📚 Responsive Story Page Documentation")]
    [Text<PERSON>rea(20, 30)]
    public string documentation = @"
🎮 RESPONSIVE STORY PAGE SYSTEM - COMPLETE GUIDE
===============================================

📱 OVERVIEW
-----------
This system makes your visual novel story page automatically adapt to different screen sizes and orientations, ensuring a great experience on phones, tablets, and desktops.

🚀 QUICK SETUP (3 Steps)
------------------------
1. Add 'ResponsiveStoryPageSetup' script to your story page GameObject
2. In the Inspector, check 'Setup On Start' or use the context menu 'Setup Responsive Design'
3. Test different screen sizes in the Game view!

📋 WHAT'S INCLUDED
------------------
✅ ResponsiveStoryPageController.cs - Main responsive logic
✅ ResponsiveStoryPageSetup.cs - Easy setup helper
✅ Enhanced StoryPage.uss - CSS with media queries for all screen sizes
✅ Smooth transitions between responsive states
✅ Orientation change handling
✅ Debug tools for testing

📱 SUPPORTED SCREEN SIZES
-------------------------
• Small Phones (≤360px): iPhone SE, older Android phones
• Standard Phones (≤480px): Most modern smartphones
• Large Phones (≤600px): iPhone Pro Max, large Android phones
• Tablets (≤800px): iPad, Android tablets
• Large Tablets (≤1024px): iPad Pro, large tablets
• Desktop (>1024px): PC, Mac, large screens

🎨 RESPONSIVE FEATURES
---------------------
• Automatic font size scaling
• Dynamic button sizing
• Character image resizing
• Dialogue box width adjustment
• UI element repositioning
• Smooth transitions (0.3s default)
• Portrait/landscape optimization

🔧 CUSTOMIZATION OPTIONS
------------------------
• Custom breakpoints for specific devices
• Adjustable transition duration
• Enable/disable orientation handling
• Debug mode for development
• Smooth transitions on/off

🧪 TESTING YOUR RESPONSIVE DESIGN
---------------------------------
1. UNITY EDITOR:
   - Change Game view resolution
   - Try different aspect ratios
   - Test portrait/landscape modes

2. RECOMMENDED TEST RESOLUTIONS:
   - iPhone SE: 375x667
   - iPhone 12: 390x844
   - iPhone Pro Max: 428x926
   - iPad: 768x1024
   - iPad Pro: 1024x1366
   - Desktop: 1920x1080

3. REAL DEVICE TESTING:
   - Build and test on actual devices
   - Check different orientations
   - Verify touch interactions

🐛 TROUBLESHOOTING
------------------
PROBLEM: Layout doesn't change when resizing
SOLUTION: Check if ResponsiveStoryPageController is enabled and UIDocument is assigned

PROBLEM: Transitions are too slow/fast
SOLUTION: Adjust 'transitionDuration' in ResponsiveStoryPageController

PROBLEM: Text is too small on phones
SOLUTION: Modify font sizes in StoryPage.uss media queries

PROBLEM: Character image is cut off
SOLUTION: Adjust character-image width/height in CSS breakpoints

PROBLEM: Buttons are too small to tap
SOLUTION: Increase button sizes in phone breakpoints (min 44px recommended)

⚙️ ADVANCED CONFIGURATION
-------------------------
1. CUSTOM BREAKPOINTS:
   - Enable 'useCustomBreakpoints' in ResponsiveStoryPageSetup
   - Set your own pixel values for different device categories

2. CSS CUSTOMIZATION:
   - Edit StoryPage.uss media queries
   - Add new responsive classes
   - Modify transition properties

3. SCRIPT CUSTOMIZATION:
   - Extend ResponsiveStoryPageController
   - Add custom responsive behaviors
   - Implement device-specific logic

📊 DEBUG MODE
-------------
Enable debug mode to see:
• Current screen size
• Active responsive class
• Screen orientation
• Real-time updates

🎯 BEST PRACTICES
-----------------
• Test on real devices, not just simulator
• Keep touch targets ≥44px for accessibility
• Use relative units when possible
• Test both portrait and landscape
• Consider safe areas on modern phones
• Optimize for one-handed use on phones

🔄 INTEGRATION WITH EXISTING SYSTEMS
------------------------------------
• Works with existing DialogueSystem
• Compatible with CinematicEffects
• Supports all StoryPage.uxml elements
• No changes needed to existing dialogue data

📞 SUPPORT
----------
If you encounter issues:
1. Check Unity Console for error messages
2. Enable debug mode to see responsive state
3. Verify UIDocument and StoryPage.uxml are properly assigned
4. Test with a simple scene first

🎉 ENJOY YOUR RESPONSIVE VISUAL NOVEL!
=====================================";

    [Header("Quick Actions")]
    public bool showQuickHelp = true;
    
    private void Start()
    {
        if (showQuickHelp)
        {
            ShowQuickStartGuide();
        }
    }
    
    private void ShowQuickStartGuide()
    {
        Debug.Log("📚 RESPONSIVE STORY PAGE - QUICK START GUIDE");
        Debug.Log("============================================");
        Debug.Log("");
        Debug.Log("🚀 TO GET STARTED:");
        Debug.Log("1. Add ResponsiveStoryPageSetup script to your story GameObject");
        Debug.Log("2. Check 'Setup On Start' in the inspector");
        Debug.Log("3. Play the scene and test different Game view resolutions!");
        Debug.Log("");
        Debug.Log("🧪 TESTING:");
        Debug.Log("• Change Game view resolution to see responsive changes");
        Debug.Log("• Try phone sizes: 375x667, 390x844, 428x926");
        Debug.Log("• Try tablet sizes: 768x1024, 1024x1366");
        Debug.Log("");
        Debug.Log("🔧 NEED HELP? Check the documentation in this script's inspector!");
    }
    
    [ContextMenu("Show Setup Instructions")]
    public void ShowSetupInstructions()
    {
        Debug.Log("📱 RESPONSIVE SETUP INSTRUCTIONS");
        Debug.Log("================================");
        Debug.Log("");
        Debug.Log("STEP 1: Add ResponsiveStoryPageSetup");
        Debug.Log("• Add the ResponsiveStoryPageSetup script to your story page GameObject");
        Debug.Log("• This GameObject should already have UIDocument with StoryPage.uxml");
        Debug.Log("");
        Debug.Log("STEP 2: Configure Settings");
        Debug.Log("• Check 'Setup On Start' for automatic setup");
        Debug.Log("• Enable 'Enable Debug Mode' to see responsive info");
        Debug.Log("• Adjust transition settings if needed");
        Debug.Log("");
        Debug.Log("STEP 3: Test Responsive Design");
        Debug.Log("• Play the scene");
        Debug.Log("• Change Game view resolution");
        Debug.Log("• Watch the layout adapt automatically!");
        Debug.Log("");
        Debug.Log("✅ That's it! Your story page is now responsive!");
    }
    
    [ContextMenu("Show Testing Guide")]
    public void ShowTestingGuide()
    {
        Debug.Log("🧪 RESPONSIVE TESTING GUIDE");
        Debug.Log("===========================");
        Debug.Log("");
        Debug.Log("UNITY EDITOR TESTING:");
        Debug.Log("• Game view → Free Aspect → Try different resolutions");
        Debug.Log("• Recommended test sizes:");
        Debug.Log("  - Phone: 375x667, 390x844, 428x926");
        Debug.Log("  - Tablet: 768x1024, 1024x1366");
        Debug.Log("  - Desktop: 1920x1080, 2560x1440");
        Debug.Log("");
        Debug.Log("REAL DEVICE TESTING:");
        Debug.Log("• Build to your target devices");
        Debug.Log("• Test portrait and landscape orientations");
        Debug.Log("• Check touch interaction sizes");
        Debug.Log("• Verify text readability");
        Debug.Log("");
        Debug.Log("DEBUG MODE:");
        Debug.Log("• Enable debug in ResponsiveStoryPageSetup");
        Debug.Log("• See real-time responsive info in bottom-left corner");
        Debug.Log("• Monitor console for responsive state changes");
    }
    
    [ContextMenu("Show Troubleshooting")]
    public void ShowTroubleshooting()
    {
        Debug.Log("🔧 RESPONSIVE TROUBLESHOOTING");
        Debug.Log("=============================");
        Debug.Log("");
        Debug.Log("COMMON ISSUES & SOLUTIONS:");
        Debug.Log("");
        Debug.Log("❌ Layout doesn't change when resizing");
        Debug.Log("✅ Check ResponsiveStoryPageController is enabled");
        Debug.Log("✅ Verify UIDocument has StoryPage.uxml assigned");
        Debug.Log("✅ Make sure 'enableResponsiveMode' is true");
        Debug.Log("");
        Debug.Log("❌ Transitions are too slow/fast");
        Debug.Log("✅ Adjust 'transitionDuration' in ResponsiveStoryPageController");
        Debug.Log("✅ Or disable 'enableSmoothTransitions' for instant changes");
        Debug.Log("");
        Debug.Log("❌ Text is too small on phones");
        Debug.Log("✅ Edit StoryPage.uss media queries");
        Debug.Log("✅ Increase font-size values in phone breakpoints");
        Debug.Log("");
        Debug.Log("❌ Buttons are too small to tap");
        Debug.Log("✅ Increase button width/height in CSS");
        Debug.Log("✅ Minimum 44px recommended for touch targets");
        Debug.Log("");
        Debug.Log("❌ Character image is cut off");
        Debug.Log("✅ Adjust character-image dimensions in CSS breakpoints");
        Debug.Log("✅ Check character-container padding-bottom");
    }
}
