using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// Setup helper for adding responsive functionality to story pages
/// Automatically configures ResponsiveStoryPageController with optimal settings
/// </summary>
public class ResponsiveStoryPageSetup : MonoBehaviour
{
    [Header("📱 Responsive Story Page Setup")]
    [TextArea(5, 8)]
    public string instructions = @"
This script automatically sets up responsive design for your story page:

✅ Adds ResponsiveStoryPageController component
✅ Configures optimal breakpoints for mobile devices
✅ Enables smooth transitions and orientation handling
✅ Sets up debug options for testing

USAGE:
1. Add this script to your story page GameObject
2. Click 'Setup Responsive Design' in the context menu
3. Test on different screen sizes and orientations

The setup will work with your existing StoryPage.uss and DialogueSystem.";

    [Header("Auto Setup")]
    public bool setupOnStart = true;
    public bool enableDebugMode = false;
    
    [Header("Custom Breakpoints (optional)")]
    public bool useCustomBreakpoints = false;
    public int customLargeTablet = 1024;
    public int customTablet = 800;
    public int customLargePhone = 600;
    public int customPhone = 480;
    public int customSmallPhone = 360;
    
    [Header("Responsive Settings")]
    public bool enableSmoothTransitions = true;
    public bool enableOrientationHandling = true;
    public float transitionDuration = 0.3f;
    
    private ResponsiveStoryPageController responsiveController;
    
    private void Start()
    {
        if (setupOnStart)
        {
            SetupResponsiveDesign();
        }
    }
    
    [ContextMenu("Setup Responsive Design")]
    public void SetupResponsiveDesign()
    {
        Debug.Log("📱 Setting up Responsive Design for Story Page...");
        
        // Check if UIDocument exists
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("❌ UIDocument component not found! Please add UIDocument with StoryPage.uxml first.");
            return;
        }
        
        // Check if ResponsiveStoryPageController already exists
        responsiveController = GetComponent<ResponsiveStoryPageController>();
        if (responsiveController == null)
        {
            responsiveController = gameObject.AddComponent<ResponsiveStoryPageController>();
            Debug.Log("✅ Added ResponsiveStoryPageController component");
        }
        else
        {
            Debug.Log("ℹ️ ResponsiveStoryPageController already exists, updating settings...");
        }
        
        // Configure responsive controller
        ConfigureResponsiveController();
        
        // Verify DialogueSystem compatibility
        VerifyDialogueSystemCompatibility();
        
        // Test responsive functionality
        TestResponsiveFunctionality();
        
        Debug.Log("✅ Responsive Design setup completed successfully!");
        Debug.Log("📱 Your story page now supports:");
        Debug.Log("   • Automatic layout adjustments for different screen sizes");
        Debug.Log("   • Smooth transitions between responsive states");
        Debug.Log("   • Orientation change handling");
        Debug.Log("   • Optimized layouts for phones, tablets, and desktops");
    }
    
    private void ConfigureResponsiveController()
    {
        if (responsiveController == null) return;
        
        // Basic settings
        responsiveController.enableResponsiveMode = true;
        responsiveController.enableOrientationHandling = enableOrientationHandling;
        responsiveController.enableSmoothTransitions = enableSmoothTransitions;
        responsiveController.transitionDuration = transitionDuration;
        
        // Debug settings
        responsiveController.showDebugInfo = enableDebugMode;
        responsiveController.logScreenChanges = enableDebugMode;
        
        // Custom breakpoints if specified
        if (useCustomBreakpoints)
        {
            responsiveController.largeTabletBreakpoint = customLargeTablet;
            responsiveController.tabletBreakpoint = customTablet;
            responsiveController.largePhoneBreakpoint = customLargePhone;
            responsiveController.phoneBreakpoint = customPhone;
            responsiveController.smallPhoneBreakpoint = customSmallPhone;
            
            Debug.Log($"✅ Applied custom breakpoints: {customSmallPhone}, {customPhone}, {customLargePhone}, {customTablet}, {customLargeTablet}");
        }
        
        Debug.Log("✅ ResponsiveStoryPageController configured successfully");
    }
    
    private void VerifyDialogueSystemCompatibility()
    {
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            Debug.LogWarning("⚠️ DialogueSystem not found. Responsive design will work, but dialogue functionality may be limited.");
            Debug.LogWarning("💡 Consider adding DialogueSystem component for full story page functionality.");
            return;
        }
        
        Debug.Log("✅ DialogueSystem found - responsive design is fully compatible");
        
        // Check if dialogue system has story data
        if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
        {
            Debug.LogWarning("⚠️ DialogueSystem has no story data. Add dialogue data to test responsive layouts with actual content.");
        }
        else
        {
            Debug.Log($"✅ Found {dialogueSystem.dialogues.Length} dialogue entries for testing");
        }
    }
    
    private void TestResponsiveFunctionality()
    {
        if (responsiveController == null) return;
        
        Debug.Log("🧪 Testing responsive functionality...");
        
        // Force an initial responsive update
        responsiveController.ForceResponsiveUpdate();
        
        // Log current responsive state
        string currentClass = responsiveController.GetCurrentResponsiveClass();
        Vector2 screenSize = responsiveController.GetCurrentScreenSize();
        
        Debug.Log($"📱 Current responsive state:");
        Debug.Log($"   • Screen size: {screenSize.x}x{screenSize.y}");
        Debug.Log($"   • Size class: {currentClass}");
        Debug.Log($"   • Orientation: {Screen.orientation}");
        
        // Provide testing suggestions
        Debug.Log("🎮 Testing suggestions:");
        Debug.Log("   • Change Game view resolution to test different screen sizes");
        Debug.Log("   • Try portrait and landscape orientations");
        Debug.Log("   • Test on actual mobile devices for best results");
        Debug.Log("   • Enable debug mode to see real-time responsive information");
    }
    
    [ContextMenu("Enable Debug Mode")]
    public void EnableDebugMode()
    {
        enableDebugMode = true;
        
        if (responsiveController != null)
        {
            responsiveController.showDebugInfo = true;
            responsiveController.logScreenChanges = true;
            Debug.Log("✅ Debug mode enabled - check bottom-left corner for responsive info");
        }
        else
        {
            Debug.LogWarning("⚠️ ResponsiveStoryPageController not found. Run setup first.");
        }
    }
    
    [ContextMenu("Disable Debug Mode")]
    public void DisableDebugMode()
    {
        enableDebugMode = false;
        
        if (responsiveController != null)
        {
            responsiveController.showDebugInfo = false;
            responsiveController.logScreenChanges = false;
            Debug.Log("✅ Debug mode disabled");
        }
    }
    
    [ContextMenu("Test Different Screen Sizes")]
    public void TestDifferentScreenSizes()
    {
        Debug.Log("📱 Recommended Game view resolutions for testing:");
        Debug.Log("   • iPhone SE: 375x667 (small phone)");
        Debug.Log("   • iPhone 12: 390x844 (standard phone)");
        Debug.Log("   • iPhone 12 Pro Max: 428x926 (large phone)");
        Debug.Log("   • iPad: 768x1024 (tablet portrait)");
        Debug.Log("   • iPad Pro: 1024x1366 (large tablet)");
        Debug.Log("   • Desktop: 1920x1080 (desktop)");
        Debug.Log("");
        Debug.Log("💡 Change the Game view resolution and watch the layout adapt automatically!");
    }
    
    [ContextMenu("Force Responsive Update")]
    public void ForceResponsiveUpdate()
    {
        if (responsiveController != null)
        {
            responsiveController.ForceResponsiveUpdate();
            Debug.Log("✅ Forced responsive update completed");
        }
        else
        {
            Debug.LogWarning("⚠️ ResponsiveStoryPageController not found. Run setup first.");
        }
    }
    
    private void OnGUI()
    {
        if (enableDebugMode && responsiveController == null)
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 80));
            GUILayout.Box("Responsive Setup Required", GUI.skin.box);
            GUILayout.Label("Click 'Setup Responsive Design'");
            GUILayout.Label("in the context menu to begin");
            GUILayout.EndArea();
        }
    }
    
    // Validation method for inspector
    private void OnValidate()
    {
        // Ensure breakpoints are in correct order
        if (useCustomBreakpoints)
        {
            if (customSmallPhone >= customPhone)
                customPhone = customSmallPhone + 120;
            if (customPhone >= customLargePhone)
                customLargePhone = customPhone + 120;
            if (customLargePhone >= customTablet)
                customTablet = customLargePhone + 200;
            if (customTablet >= customLargeTablet)
                customLargeTablet = customTablet + 224;
        }
        
        // Ensure transition duration is reasonable
        if (transitionDuration < 0.1f)
            transitionDuration = 0.1f;
        if (transitionDuration > 2f)
            transitionDuration = 2f;
    }
}
