%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!115 &316569612
MonoScript:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 7
  m_DefaultReferences: {}
  m_Icon: {fileID: 0}
  m_Type: 0
  m_ExecutionOrder: 0
  m_ClassName: StorySceneController
  m_Namespace: 
  m_AssemblyName: Assembly-CSharp
--- !u!1 &347974943
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 347974944}
  - component: {fileID: 347974945}
  m_Layer: 0
  m_Name: UIDocument
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &347974944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 347974943}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 384327703}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &347974945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 347974943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PanelSettings: {fileID: 11400000, guid: 3a7d9b803338b2548afccd245979a828, type: 2}
  m_ParentUI: {fileID: 384327710}
  sourceAsset: {fileID: 9197481963319205126, guid: 4b2ebbb2cfb56e14886d78e274080344, type: 3}
  m_SortingOrder: 0
  m_WorldSpaceSizeMode: 1
  m_WorldSpaceWidth: 1920
  m_WorldSpaceHeight: 1080
--- !u!1 &384327701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 384327703}
  - component: {fileID: 384327702}
  - component: {fileID: 384327707}
  - component: {fileID: 384327705}
  - component: {fileID: 384327704}
  - component: {fileID: 384327708}
  - component: {fileID: 384327711}
  - component: {fileID: 384327710}
  - component: {fileID: 384327709}
  - component: {fileID: 384327712}
  - component: {fileID: 384327713}
  - component: {fileID: 384327714}
  m_Layer: 0
  m_Name: VisualNovelManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &384327702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe3a9d98d21c0144595a467121c85da9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  setupInstructions: "\nVISUAL NOVEL SETUP GUIDE\n========================\n\n1.
    SCENE SETUP:\n   - Add this script to a GameObject in your story scene\n   -
    Make sure you have a UIDocument component on the same GameObject\n   - Assign
    the StoryPage.uxml file to the UIDocument\n\n2. REQUIRED SCRIPTS:\n   - DialogueSystem.cs
    (handles dialogue flow)\n   - StorySceneController.cs (manages scene controls)\n  
    - CinematicEffects.cs (premium visual effects)\n\n3. FOLDER STRUCTURE:\n   Assets/\n  
    \u251C\u2500\u2500 Resources/\n   \u2502   \u251C\u2500\u2500 Background/    
    (put background images here)\n   \u2502   \u251C\u2500\u2500 characters/    
    (put character sprites here)\n   \u2502   \u2514\u2500\u2500 Audio/         
    (put voice clips here)\n   \u251C\u2500\u2500 Scripts/\n   \u251C\u2500\u2500
    UI/\n   \u2514\u2500\u2500 Scenes/\n\n4. CREATING STORY DATA:\n   - Right-click
    in Project \u2192 Create \u2192 Visual Novel \u2192 Story Data\n   - Use the
    context menu 'Create Sample Story' to generate example data\n   - Customize the
    dialogue array for your story\n\n5. COMPONENT SETUP:\n   - Add DialogueSystem,
    StorySceneController, and CinematicEffects to your story GameObject\n   - Assign
    the story data to DialogueSystem.dialogues array\n   - Configure settings in
    each component as needed\n\n6. TESTING:\n   - Play the scene\n   - Use SPACE
    or ENTER to advance dialogue\n   - Use A for Auto mode, L for Log, S for Skip,
    ESC for Menu\n   - Click buttons or use keyboard shortcuts\n\n7. CUSTOMIZATION:\n  
    - Modify StoryPage.uss for visual styling\n   - Add your own background images
    and character sprites\n   - Record voice lines and add them to Resources/Audio/\n  
    - Adjust timing and effects in the inspector\n"
  autoSetupOnStart: 0
  storyData: {fileID: 11400000, guid: d9697e5786946704fb5127843b138612, type: 2}
--- !u!4 &384327703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.29956, y: -0.42389, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 347974944}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &384327704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0d14fee6476f3943acc646163ce53e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  instructions: '

    This script will fix all the console errors:

    1. Create
    and assign SampleStoryData asset

    2. Add UIDocument component with StoryPage.uxml

    3.
    Fix ArgumentOutOfRangeException with bounds checking

    4. Setup proper image
    loading for Maimy characters


    Click ''Fix All Issues'' button in the inspector
    or run in play mode.

'
  storyDataAsset: {fileID: 0}
  storyPageUxml: {fileID: 0}
  storyPageUss: {fileID: 0}
--- !u!114 &384327705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a36be512688a615419e6fba1d0bdd15a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  enableParallax: 0
  parallaxStrength: 0.02
  parallaxSmoothness: 2
  shakeIntensity: 0.1
  shakeDuration: 0.5
  enableLightingOverlay: 1
  lightingGradient:
    serializedVersion: 2
    key0: {r: 1, g: 1, b: 1, a: 1}
    key1: {r: 1, g: 1, b: 1, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  lightingCycleSpeed: 1
--- !u!114 &384327707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c889ccc0b36caed4a899e1061367ae52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogues:
  - characterName: Narrator
    dialogueText: Welcome to the world of visual novels! This is Maimy's story.
    backgroundImage: MaimyBackground
    characterImage: 
    characterPosition: center
    autoDelay: 4
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: Hello there! I'm Maimy, your AI companion. Nice to meet you!
    backgroundImage: MaimyBackground
    characterImage: maimysenyummanis
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: I've been waiting for someone like you to come along. Would you
      like to be my friend?
    backgroundImage: MaimyBackground
    characterImage: maimycuriouscute
    characterPosition: right
    autoDelay: 4
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: So, what do you think? Will you be my friend?
    backgroundImage: MaimyBackground
    characterImage: maimymerenungmata
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 1
    choices:
    - Of course! I'd love to be your friend!
    - I need to think about it...
    - Maybe we can start as acquaintances?
    choiceTargets: 04000000070000000a000000
  - characterName: Maimy
    dialogueText: Really?! That makes me so happy! I knew you were special!
    backgroundImage: MaimyBackground
    characterImage: maimy (1)
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: Let me show you around! There's so much I want to share with you!
    backgroundImage: taman paradise (1)
    characterImage: maimy (2)
    characterPosition: center
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: This is going to be the start of a wonderful friendship!
    backgroundImage: taman paradise (2)
    characterImage: maimy (3)
    characterPosition: center
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: Oh... I understand. Take all the time you need.
    backgroundImage: MaimyBackground
    characterImage: maimyketawakecil
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: I'll be here when you're ready. I believe good things come to those
      who wait.
    backgroundImage: MaimyBackground
    characterImage: maimymerenungmata
    characterPosition: right
    autoDelay: 4
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: Until then, I hope we can at least talk sometimes?
    backgroundImage: MaimyBackground
    characterImage: maimycuriouscute
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: Acquaintances... that's a good start! Everyone has to begin somewhere,
      right?
    backgroundImage: MaimyBackground
    characterImage: maimymerenungmata
    characterPosition: right
    autoDelay: 4
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: I'm patient. I believe that with time, we can become great friends!
    backgroundImage: MaimyBackground
    characterImage: maimysenyummanis
    characterPosition: right
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: Maimy
    dialogueText: For now, let me show you what I can do as your AI companion!
    backgroundImage: taman paradise (3)
    characterImage: maimy (4)
    characterPosition: center
    autoDelay: 3
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  - characterName: System
    dialogueText: This concludes Maimy's story. Thank you for spending time with
      her!
    backgroundImage: taman paradise (3)
    characterImage: 
    characterPosition: center
    autoDelay: 5
    audioClip: 
    isChoice: 0
    choices: []
    choiceTargets: 
  textSpeed: 0.05
  autoMode: 0
--- !u!114 &384327708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a122fe037a580cf4f94c8918c4221d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  instructions: '

    MASALAH YANG TERDETEKSI:

    1. No story data assigned

    2.
    UIDocument component missing


    SOLUSI:

    Klik tombol ''Fix Now'' di bawah
    atau jalankan scene.

    Script ini akan otomatis memperbaiki semua masalah!

'
  autoFixOnStart: 1
--- !u!114 &384327709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f3ef69216adb7d64eaf8563427f4ffc6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  autoFixOnStart: 1
  fallbackStoryData: {fileID: 0}
--- !u!114 &384327710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PanelSettings: {fileID: 11400000, guid: 3a7d9b803338b2548afccd245979a828, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: 4b2ebbb2cfb56e14886d78e274080344, type: 3}
  m_SortingOrder: 0
  m_WorldSpaceSizeMode: 1
  m_WorldSpaceWidth: 1920
  m_WorldSpaceHeight: 1080
--- !u!114 &384327711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 316569612}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogueSystem: {fileID: 384327707}
  cinematicEffects: {fileID: 384327705}
  nextKey: 32
  autoKey: 97
  logKey: 108
  skipKey: 115
  menuKey: 27
--- !u!114 &384327712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8734364e385c734cac2d2e17adfde24, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  info: This script fixes Input System conflicts by providing a safe input handling
    method.
  nextKey: 32
  autoKey: 97
  logKey: 108
  skipKey: 115
  menuKey: 27
--- !u!114 &384327713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 711a8fa7958ba3e439d930379e5590af, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  info: This script fixes Input System conflicts in CinematicEffects by safely handling
    mouse input.
  autoFixOnStart: 1
  disableParallaxOnConflict: 1
--- !u!114 &384327714
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 384327701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f357ccae719c4984eb3c04a61840a982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  info: '

    This script fixes broken asset references like:

    - Broken text
    PPtr with GUID 00000000000000000000000000000000

    - Missing script references

    -
    Corrupted ScriptableObject assets

    - Invalid fileID references


    Click
    ''Fix Broken Assets'' to repair automatically.

'
  autoFixOnStart: 1
  createBackups: 1
--- !u!1 &519420028
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519420032}
  - component: {fileID: 519420031}
  - component: {fileID: 519420029}
  - component: {fileID: 519420030}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &519420029
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
--- !u!114 &519420030
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!20 &519420031
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 34
  orthographic: 1
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 0
  m_HDR: 1
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &519420032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519420028}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &619394800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 619394802}
  - component: {fileID: 619394801}
  m_Layer: 0
  m_Name: Global Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &619394801
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 619394800}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 4
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 2
  m_UseNormalMap: 0
  m_ShadowsEnabled: 0
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0
  m_PointLightOuterRadius: 1
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!4 &619394802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 619394800}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 519420032}
  - {fileID: 619394802}
  - {fileID: 384327703}
