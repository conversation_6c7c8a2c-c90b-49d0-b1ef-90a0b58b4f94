using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

/// <summary>
/// Handles responsive design for the story page UI
/// Monitors screen size changes and applies appropriate CSS classes
/// Provides dynamic layout adjustments for different device sizes
/// </summary>
public class ResponsiveStoryPageController : MonoBehaviour
{
    [Header("📱 Responsive Story Page Controller")]
    [TextArea(3, 5)]
    public string info = @"
This script handles responsive design for the story page:
- Monitors screen size changes
- Applies appropriate CSS classes for different devices
- Handles orientation changes
- Provides smooth transitions between layouts";

    [Header("Settings")]
    public bool enableResponsiveMode = true;
    public bool enableOrientationHandling = true;
    public bool enableSmoothTransitions = true;
    public float transitionDuration = 0.3f;
    
    [Header("Breakpoints (pixels)")]
    public int largeTabletBreakpoint = 1024;
    public int tabletBreakpoint = 800;
    public int largePhoneBreakpoint = 600;
    public int phoneBreakpoint = 480;
    public int smallPhoneBreakpoint = 360;
    
    [Header("Debug")]
    public bool showDebugInfo = false;
    public bool logScreenChanges = true;
    
    // Private variables
    private UIDocument uiDocument;
    private VisualElement root;
    private Vector2 lastScreenSize;
    private ScreenOrientation lastOrientation;
    private string currentSizeClass = "";
    private Coroutine transitionCoroutine;
    
    // UI Elements for responsive adjustments
    private VisualElement storyContainer;
    private VisualElement dialogueBox;
    private VisualElement characterImage;
    private VisualElement nameBox;
    private VisualElement topBar;
    private VisualElement choicesContainer;
    
    private void Start()
    {
        InitializeResponsiveController();
        
        if (enableResponsiveMode)
        {
            StartCoroutine(MonitorScreenChanges());
        }
    }
    
    private void InitializeResponsiveController()
    {
        Debug.Log("📱 Initializing Responsive Story Page Controller...");
        
        // Get UI Document
        uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("❌ UIDocument not found! ResponsiveStoryPageController requires UIDocument component.");
            enabled = false;
            return;
        }
        
        root = uiDocument.rootVisualElement;
        if (root == null)
        {
            Debug.LogError("❌ Root visual element not found!");
            enabled = false;
            return;
        }
        
        // Cache UI elements
        CacheUIElements();
        
        // Set initial screen size and orientation
        lastScreenSize = new Vector2(Screen.width, Screen.height);
        lastOrientation = Screen.orientation;
        
        // Apply initial responsive class
        ApplyResponsiveClass();
        
        Debug.Log("✅ Responsive Story Page Controller initialized successfully!");
    }
    
    private void CacheUIElements()
    {
        storyContainer = root.Q("story-container");
        dialogueBox = root.Q("dialogue-box");
        characterImage = root.Q("character-image");
        nameBox = root.Q("name-box");
        topBar = root.Q("top-bar");
        choicesContainer = root.Q("choices-container");
        
        if (showDebugInfo)
        {
            Debug.Log($"UI Elements cached: Container={storyContainer != null}, Dialogue={dialogueBox != null}, Character={characterImage != null}");
        }
    }
    
    private IEnumerator MonitorScreenChanges()
    {
        while (enabled && enableResponsiveMode)
        {
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            ScreenOrientation currentOrientation = Screen.orientation;
            
            // Check for screen size changes
            if (currentScreenSize != lastScreenSize || 
                (enableOrientationHandling && currentOrientation != lastOrientation))
            {
                if (logScreenChanges)
                {
                    Debug.Log($"📱 Screen change detected: {lastScreenSize} → {currentScreenSize}, Orientation: {lastOrientation} → {currentOrientation}");
                }
                
                lastScreenSize = currentScreenSize;
                lastOrientation = currentOrientation;
                
                // Apply responsive changes with smooth transition
                if (enableSmoothTransitions)
                {
                    StartSmoothTransition();
                }
                else
                {
                    ApplyResponsiveClass();
                }
            }
            
            yield return new WaitForSeconds(0.1f); // Check 10 times per second
        }
    }
    
    private void StartSmoothTransition()
    {
        if (transitionCoroutine != null)
        {
            StopCoroutine(transitionCoroutine);
        }
        
        transitionCoroutine = StartCoroutine(SmoothTransitionCoroutine());
    }
    
    private IEnumerator SmoothTransitionCoroutine()
    {
        // Add transition class
        if (root != null)
        {
            root.AddToClassList("responsive-transition");
        }
        
        // Wait a frame for the transition to start
        yield return null;
        
        // Apply new responsive class
        ApplyResponsiveClass();
        
        // Wait for transition to complete
        yield return new WaitForSeconds(transitionDuration);
        
        // Remove transition class
        if (root != null)
        {
            root.RemoveFromClassList("responsive-transition");
        }
        
        transitionCoroutine = null;
    }
    
    private void ApplyResponsiveClass()
    {
        if (root == null) return;
        
        // Remove all existing responsive classes
        RemoveAllResponsiveClasses();
        
        // Determine current size class
        string newSizeClass = GetCurrentSizeClass();
        
        if (newSizeClass != currentSizeClass)
        {
            currentSizeClass = newSizeClass;
            
            // Add appropriate responsive class
            root.AddToClassList(currentSizeClass);
            
            // Apply orientation class if enabled
            if (enableOrientationHandling)
            {
                ApplyOrientationClass();
            }
            
            // Apply additional responsive adjustments
            ApplyDynamicAdjustments();
            
            if (logScreenChanges)
            {
                Debug.Log($"📱 Applied responsive class: {currentSizeClass}");
            }
        }
    }
    
    private void RemoveAllResponsiveClasses()
    {
        root.RemoveFromClassList("large-tablet");
        root.RemoveFromClassList("tablet");
        root.RemoveFromClassList("large-phone");
        root.RemoveFromClassList("phone");
        root.RemoveFromClassList("small-phone");
        root.RemoveFromClassList("portrait");
        root.RemoveFromClassList("landscape");
    }
    
    private string GetCurrentSizeClass()
    {
        int screenWidth = Screen.width;
        
        if (screenWidth <= smallPhoneBreakpoint)
            return "small-phone";
        else if (screenWidth <= phoneBreakpoint)
            return "phone";
        else if (screenWidth <= largePhoneBreakpoint)
            return "large-phone";
        else if (screenWidth <= tabletBreakpoint)
            return "tablet";
        else if (screenWidth <= largeTabletBreakpoint)
            return "large-tablet";
        else
            return "desktop";
    }
    
    private void ApplyOrientationClass()
    {
        if (Screen.width > Screen.height)
        {
            root.AddToClassList("landscape");
            root.RemoveFromClassList("portrait");
        }
        else
        {
            root.AddToClassList("portrait");
            root.RemoveFromClassList("landscape");
        }
    }
    
    private void ApplyDynamicAdjustments()
    {
        // Apply dynamic adjustments based on screen size
        float screenRatio = (float)Screen.width / Screen.height;
        
        // Adjust character image size based on screen ratio
        if (characterImage != null)
        {
            AdjustCharacterImageSize(screenRatio);
        }
        
        // Adjust dialogue box positioning
        if (dialogueBox != null)
        {
            AdjustDialogueBoxSize();
        }
    }
    
    private void AdjustCharacterImageSize(float screenRatio)
    {
        // Dynamic character image sizing based on screen ratio
        if (currentSizeClass == "small-phone" || currentSizeClass == "phone")
        {
            if (screenRatio < 0.6f) // Very tall screens
            {
                characterImage.style.width = 140;
                characterImage.style.height = 210;
            }
            else if (screenRatio > 1.5f) // Very wide screens (landscape)
            {
                characterImage.style.width = 160;
                characterImage.style.height = 240;
            }
        }

        // Ensure character stays at bottom
        if (characterImage != null)
        {
            characterImage.style.alignSelf = Align.Center;
        }
    }
    
    private void AdjustDialogueBoxSize()
    {
        // Dynamic dialogue box adjustments
        if (currentSizeClass == "small-phone")
        {
            if (Screen.height < 600) // Very short screens
            {
                dialogueBox.style.minHeight = 70;
            }
        }
    }
    
    // Public methods for external control
    public void ForceResponsiveUpdate()
    {
        ApplyResponsiveClass();
    }
    
    public void SetResponsiveMode(bool enabled)
    {
        enableResponsiveMode = enabled;
        
        if (enabled)
        {
            StartCoroutine(MonitorScreenChanges());
        }
    }
    
    public string GetCurrentResponsiveClass()
    {
        return currentSizeClass;
    }
    
    public Vector2 GetCurrentScreenSize()
    {
        return new Vector2(Screen.width, Screen.height);
    }
    
    private void OnGUI()
    {
        if (showDebugInfo)
        {
            GUILayout.BeginArea(new Rect(10, Screen.height - 150, 300, 140));
            GUILayout.Box("Responsive Debug Info", GUI.skin.box);
            GUILayout.Label($"Screen: {Screen.width}x{Screen.height}");
            GUILayout.Label($"Orientation: {Screen.orientation}");
            GUILayout.Label($"Size Class: {currentSizeClass}");
            GUILayout.Label($"Ratio: {(float)Screen.width / Screen.height:F2}");
            GUILayout.Label($"Responsive Mode: {enableResponsiveMode}");
            GUILayout.EndArea();
        }
    }
    
    private void OnDisable()
    {
        if (transitionCoroutine != null)
        {
            StopCoroutine(transitionCoroutine);
        }
    }
}
