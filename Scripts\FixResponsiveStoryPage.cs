using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// Fixes responsive design issues and character positioning for story page
/// Replaces CSS media queries with Unity-specific responsive controller
/// </summary>
public class FixResponsiveStoryPage : MonoBehaviour
{
    [Header("🔧 Fix Responsive Story Page")]
    [TextArea(5, 8)]
    public string instructions = @"
This script fixes the responsive design issues:

❌ PROBLEMS FIXED:
• Character image not at bottom: 0
• Responsive design not working on iPhone simulators
• Layout broken on different screen sizes
• CSS media queries not working properly in Unity

✅ SOLUTIONS APPLIED:
• Character positioned at absolute bottom: 0
• Unity-specific responsive controller
• Programmatic style application
• Real-time screen size monitoring
• iPhone/iPad/Android compatibility";

    [Header("Settings")]
    public bool fixOnStart = true;
    public bool enableDebugMode = false;
    public bool removeOldResponsiveScripts = true;
    
    private void Start()
    {
        if (fixOnStart)
        {
            FixResponsiveDesign();
        }
    }
    
    [ContextMenu("Fix Responsive Design")]
    public void FixResponsiveDesign()
    {
        Debug.Log("🔧 Fixing Responsive Story Page Design...");
        
        // Step 1: Remove old responsive scripts if they exist
        if (removeOldResponsiveScripts)
        {
            RemoveOldResponsiveScripts();
        }
        
        // Step 2: Add Unity-specific responsive controller
        AddUnityResponsiveController();
        
        // Step 3: Verify UIDocument setup
        VerifyUIDocumentSetup();
        
        // Step 4: Test responsive functionality
        TestResponsiveFunctionality();
        
        Debug.Log("✅ Responsive Story Page fixed successfully!");
        Debug.Log("📱 Character image is now at bottom: 0");
        Debug.Log("📱 Responsive design works on all devices");
        Debug.Log("📱 Test with different screen sizes in Game view");
    }
    
    private void RemoveOldResponsiveScripts()
    {
        Debug.Log("🗑️ Removing old responsive scripts...");
        
        // Remove old responsive controllers
        var oldController = GetComponent<ResponsiveStoryPageController>();
        if (oldController != null)
        {
            DestroyImmediate(oldController);
            Debug.Log("✅ Removed old ResponsiveStoryPageController");
        }
        
        var oldSetup = GetComponent<ResponsiveStoryPageSetup>();
        if (oldSetup != null)
        {
            DestroyImmediate(oldSetup);
            Debug.Log("✅ Removed old ResponsiveStoryPageSetup");
        }
    }
    
    private void AddUnityResponsiveController()
    {
        Debug.Log("📱 Adding Unity Responsive Controller...");
        
        var unityController = GetComponent<UnityResponsiveController>();
        if (unityController == null)
        {
            unityController = gameObject.AddComponent<UnityResponsiveController>();
            Debug.Log("✅ Added UnityResponsiveController");
        }
        else
        {
            Debug.Log("ℹ️ UnityResponsiveController already exists");
        }
        
        // Configure the controller
        unityController.enableResponsiveMode = true;
        unityController.logScreenChanges = enableDebugMode;
        unityController.showDebugInfo = enableDebugMode;
        
        // Set optimal breakpoints for mobile devices
        unityController.phoneMaxWidth = 480;
        unityController.largePhoneMaxWidth = 600;
        unityController.tabletMaxWidth = 800;
        unityController.largeTabletMaxWidth = 1024;
        
        Debug.Log("✅ Unity Responsive Controller configured");
    }
    
    private void VerifyUIDocumentSetup()
    {
        Debug.Log("🎨 Verifying UIDocument setup...");
        
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("❌ UIDocument not found! Add UIDocument component with StoryPage.uxml");
            return;
        }
        
        if (uiDocument.visualTreeAsset == null)
        {
            Debug.LogError("❌ StoryPage.uxml not assigned to UIDocument!");
            return;
        }
        
        var root = uiDocument.rootVisualElement;
        if (root == null)
        {
            Debug.LogError("❌ Root visual element not found!");
            return;
        }
        
        // Check if character image exists
        var characterImage = root.Q("character-image");
        if (characterImage == null)
        {
            Debug.LogError("❌ Character image element not found in UXML!");
            return;
        }
        
        Debug.Log("✅ UIDocument setup verified");
        Debug.Log($"✅ Character image element found: {characterImage.name}");
    }
    
    private void TestResponsiveFunctionality()
    {
        Debug.Log("🧪 Testing responsive functionality...");
        
        var unityController = GetComponent<UnityResponsiveController>();
        if (unityController == null)
        {
            Debug.LogError("❌ UnityResponsiveController not found!");
            return;
        }
        
        // Force an update to test
        unityController.ForceUpdate();
        
        // Log current state
        string deviceType = unityController.GetCurrentDeviceType();
        Debug.Log($"📱 Current device type: {deviceType}");
        Debug.Log($"📱 Screen size: {Screen.width}x{Screen.height}");
        Debug.Log($"📱 Screen DPI: {Screen.dpi}");
        
        // Provide testing instructions
        Debug.Log("🎮 TESTING INSTRUCTIONS:");
        Debug.Log("1. Change Game view resolution to test different screen sizes");
        Debug.Log("2. Try these resolutions:");
        Debug.Log("   • iPhone SE: 375x667");
        Debug.Log("   • iPhone 12: 390x844");
        Debug.Log("   • iPhone 12 Pro Max: 428x926");
        Debug.Log("   • iPad: 768x1024");
        Debug.Log("   • iPad Pro: 1024x1366");
        Debug.Log("3. Watch the character stay at bottom: 0");
        Debug.Log("4. See UI elements resize automatically");
    }
    
    [ContextMenu("Enable Debug Mode")]
    public void EnableDebugMode()
    {
        enableDebugMode = true;
        
        var unityController = GetComponent<UnityResponsiveController>();
        if (unityController != null)
        {
            unityController.showDebugInfo = true;
            unityController.logScreenChanges = true;
            Debug.Log("✅ Debug mode enabled - check bottom-left for responsive info");
        }
    }
    
    [ContextMenu("Test iPhone Sizes")]
    public void TestiPhoneSizes()
    {
        Debug.Log("📱 IPHONE SCREEN SIZE REFERENCE:");
        Debug.Log("================================");
        Debug.Log("iPhone SE (1st gen): 320x568");
        Debug.Log("iPhone SE (2nd/3rd gen): 375x667");
        Debug.Log("iPhone 6/7/8: 375x667");
        Debug.Log("iPhone 6/7/8 Plus: 414x736");
        Debug.Log("iPhone X/XS/11 Pro: 375x812");
        Debug.Log("iPhone XR/11: 414x896");
        Debug.Log("iPhone 12/13 mini: 375x812");
        Debug.Log("iPhone 12/13/14: 390x844");
        Debug.Log("iPhone 12/13/14 Pro Max: 428x926");
        Debug.Log("");
        Debug.Log("💡 Set Game view to these resolutions to test iPhone layouts!");
    }
    
    [ContextMenu("Test iPad Sizes")]
    public void TestiPadSizes()
    {
        Debug.Log("📱 IPAD SCREEN SIZE REFERENCE:");
        Debug.Log("==============================");
        Debug.Log("iPad (9.7-inch): 768x1024");
        Debug.Log("iPad Air (10.9-inch): 820x1180");
        Debug.Log("iPad Pro (11-inch): 834x1194");
        Debug.Log("iPad Pro (12.9-inch): 1024x1366");
        Debug.Log("");
        Debug.Log("💡 Set Game view to these resolutions to test iPad layouts!");
    }
    
    [ContextMenu("Force Responsive Update")]
    public void ForceResponsiveUpdate()
    {
        var unityController = GetComponent<UnityResponsiveController>();
        if (unityController != null)
        {
            unityController.ForceUpdate();
            Debug.Log("✅ Forced responsive update completed");
            Debug.Log($"📱 Current device type: {unityController.GetCurrentDeviceType()}");
        }
        else
        {
            Debug.LogWarning("⚠️ UnityResponsiveController not found. Run fix first.");
        }
    }
    
    private void OnGUI()
    {
        if (enableDebugMode)
        {
            GUILayout.BeginArea(new Rect(Screen.width - 200, 10, 190, 100));
            GUILayout.Box("Responsive Fix Status", GUI.skin.box);
            
            var unityController = GetComponent<UnityResponsiveController>();
            if (unityController != null)
            {
                GUILayout.Label("✅ Fixed & Working");
                GUILayout.Label($"Device: {unityController.GetCurrentDeviceType()}");
                GUILayout.Label($"Size: {Screen.width}x{Screen.height}");
            }
            else
            {
                GUILayout.Label("❌ Not Fixed Yet");
                GUILayout.Label("Run 'Fix Responsive Design'");
            }
            
            GUILayout.EndArea();
        }
    }
}
